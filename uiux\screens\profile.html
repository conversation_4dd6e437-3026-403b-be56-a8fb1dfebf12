<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 智慧养老平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f8f9fa;
        }
        .status-bar {
            height: 44px;
            background: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            color: #000;
        }
        .nav-bar {
            height: 56px;
            background: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 56px;
            background: #ffffff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
            padding: 0 16px;
            z-index: 100;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #666;
            font-size: 12px;
        }
        .nav-item.active {
            color: #2563eb;
        }
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px;
            background: white;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.2s;
        }
        .menu-item:active {
            background: #f8f9fa;
        }
        .menu-item i {
            width: 24px;
            text-align: center;
            margin-right: 12px;
            color: #6b7280;
            font-size: 18px;
        }
        .menu-item .text {
            flex: 1;
            font-size: 15px;
            color: #1f2937;
        }
        .menu-item .badge {
            background: #ef4444;
            color: white;
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: 8px;
        }
        .menu-item .arrow {
            color: #9ca3af;
            font-size: 12px;
            margin-left: 8px;
        }
        .profile-header {
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            color: white;
            padding: 24px 16px;
            position: relative;
            margin-bottom: 16px;
        }
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }
        .profile-avatar i {
            font-size: 36px;
            color: white;
        }
        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .profile-name {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        .profile-id {
            text-align: center;
            font-size: 13px;
            opacity: 0.9;
            margin-bottom: 12px;
        }
        .profile-edit {
            position: absolute;
            top: 16px;
            right: 16px;
            background: rgba(255, 255, 255, 0.2);
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(5px);
        }
        .stats-container {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 12px 0;
            margin: 0 16px 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .stat-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            position: relative;
        }
        .stat-item:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            width: 1px;
            height: 40%;
            background: #f0f0f0;
        }
        .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 12px;
            color: #6b7280;
        }
        .section-title {
            font-size: 14px;
            color: #9ca3af;
            padding: 12px 16px 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .menu-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            background: white;
            padding: 12px 0;
            margin-bottom: 16px;
        }
        .menu-grid-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 0;
        }
        .menu-grid-item i {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 6px;
            color: #3b82f6;
            font-size: 16px;
        }
        .menu-grid-item .label {
            font-size: 12px;
            color: #4b5563;
            text-align: center;
            margin-top: 2px;
        }
        .badge-primary {
            background: #eff6ff;
            color: #2563eb;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            margin-top: 4px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex space-x-1">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <h1 class="text-lg font-bold">个人中心</h1>
        <div class="flex space-x-4">
            <i class="fas fa-cog text-gray-600"></i>
        </div>
    </div>

    <!-- 主内容 -->
    <main class="pb-20">
        <!-- 个人信息头部 -->
        <div class="profile-header">
            <div class="profile-avatar">
                <i class="fas fa-user"></i>
                <!-- 如果有头像图片，使用下面的img标签 -->
                <!-- <img src="avatar.jpg" alt="用户头像"> -->
            </div>
            <div class="profile-name">张爷爷</div>
            <div class="profile-id">ID: YL20230615001</div>
            <a href="#" class="profile-edit">
                <i class="fas fa-pen"></i>
            </a>
        </div>

        <!-- 健康数据统计 -->
        <div class="stats-container">
            <div class="stat-item">
                <div class="stat-value">36.5°C</div>
                <div class="stat-label">体温</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">125/80</div>
                <div class="stat-label">血压</div>
                <span class="badge-primary">正常</span>
            </div>
            <div class="stat-item">
                <div class="stat-value">72</div>
                <div class="stat-label">心率</div>
                <span class="badge-primary">正常</span>
            </div>
            <div class="stat-item">
                <div class="stat-value">98%</div>
                <div class="stat-label">血氧</div>
                <span class="badge-primary">正常</span>
            </div>
        </div>

        <!-- 快捷功能 -->
        <div class="menu-grid">
            <a href="#" class="menu-grid-item">
                <i class="fas fa-heartbeat"></i>
                <span class="label">健康档案</span>
            </a>
            <a href="#" class="menu-grid-item">
                <i class="fas fa-pills"></i>
                <span class="label">用药提醒</span>
            </a>
            <a href="#" class="menu-grid-item">
                <i class="fas fa-calendar-check"></i>
                <span class="label">预约记录</span>
            </a>
            <a href="#" class="menu-grid-item">
                <i class="fas fa-file-invoice"></i>
                <span class="label">消费记录</span>
            </a>
        </div>

        <!-- 我的服务 -->
        <div class="section-title">我的服务</div>
        <div class="menu-item">
            <i class="fas fa-home"></i>
            <div class="text">我的家庭</div>
            <div class="arrow"><i class="fas fa-chevron-right"></i></div>
        </div>
        <div class="menu-item">
            <i class="fas fa-user-friends"></i>
            <div class="text">紧急联系人</div>
            <div class="arrow"><i class="fas fa-chevron-right"></i></div>
        </div>
        <div class="menu-item">
            <i class="fas fa-heart"></i>
            <div class="text">我的收藏</div>
            <div class="arrow"><i class="fas fa-chevron-right"></i></div>
        </div>
        <div class="menu-item">
            <i class="fas fa-comment-dots"></i>
            <div class="text">我的评价</div>
            <div class="arrow"><i class="fas fa-chevron-right"></i></div>
        </div>

        <!-- 系统设置 -->
        <div class="section-title">系统设置</div>
        <div class="menu-item">
            <i class="fas fa-bell"></i>
            <div class="text">通知设置</div>
            <div class="arrow"><i class="fas fa-chevron-right"></i></div>
        </div>
        <div class="menu-item">
            <i class="fas fa-shield-alt"></i>
            <div class="text">隐私设置</div>
            <div class="arrow"><i class="fas fa-chevron-right"></i></div>
        </div>
        <div class="menu-item">
            <i class="fas fa-question-circle"></i>
            <div class="text">帮助中心</div>
            <div class="arrow"><i class="fas fa-chevron-right"></i></div>
        </div>
        <div class="menu-item">
            <i class="fas fa-info-circle"></i>
            <div class="text">关于我们</div>
            <div class="arrow"><i class="fas fa-chevron-right"></i></div>
        </div>
        <div class="menu-item">
            <i class="fas fa-sign-out-alt"></i>
            <div class="text" style="color: #ef4444;">退出登录</div>
        </div>
    </main>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <a href="home.html" class="nav-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="health.html" class="nav-item">
            <i class="fas fa-heartbeat"></i>
            <span>健康</span>
        </a>
        <a href="services.html" class="nav-item">
            <i class="fas fa-concierge-bell"></i>
            <span>服务</span>
        </a>
        <a href="messages.html" class="nav-item">
            <i class="fas fa-comment-alt"></i>
            <span>消息</span>
            <span class="absolute top-0 right-0 inline-block w-2 h-2 bg-red-500 rounded-full"></span>
        </a>
        <a href="profile.html" class="nav-item active">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>

    <script>
        // 导航激活状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 菜单项点击效果
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                // 在实际应用中，这里会跳转到相应的页面
                // window.location.href = 'some-page.html';
            });
        });

        // 网格菜单项点击效果
        document.querySelectorAll('.menu-grid-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                // 在实际应用中，这里会跳转到相应的页面
                // window.location.href = 'some-page.html';
            });
        });
    </script>
</body>
</html>
