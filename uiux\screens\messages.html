<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息中心 - 智慧养老平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f8f9fa;
        }
        .status-bar {
            height: 44px;
            background: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            color: #000;
        }
        .nav-bar {
            height: 56px;
            background: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 56px;
            background: #ffffff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
            padding: 0 16px;
            z-index: 100;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #666;
            font-size: 12px;
        }
        .nav-item.active {
            color: #2563eb;
        }
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .tab-item {
            padding: 12px 0;
            margin: 0 12px;
            font-size: 15px;
            color: #666;
            position: relative;
            white-space: nowrap;
        }
        .tab-item.active {
            color: #2563eb;
            font-weight: 500;
        }
        .tab-item.active:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background-color: #2563eb;
            border-radius: 3px 3px 0 0;
        }
        .message-item {
            display: flex;
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            background: #fff;
            transition: background 0.2s;
        }
        .message-item:active {
            background: #f8f9fa;
        }
        .message-item.unread {
            background-color: #f0f7ff;
        }
        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            flex-shrink: 0;
            color: #6c757d;
            font-size: 22px;
            position: relative;
        }
        .avatar img {
            width: 100%;
            height: 100%;
            border-radius: 12px;
            object-fit: cover;
        }
        .badge {
            position: absolute;
            top: -4px;
            right: -4px;
            background-color: #ef4444;
            color: white;
            border-radius: 10px;
            min-width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 4px;
            border: 2px solid white;
        }
        .message-content {
            flex: 1;
            min-width: 0;
        }
        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }
        .message-sender {
            font-weight: 500;
            font-size: 15px;
            color: #1a1a1a;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-right: 8px;
        }
        .message-time {
            font-size: 12px;
            color: #9ca3af;
            white-space: nowrap;
        }
        .message-preview {
            font-size: 13px;
            color: #6b7280;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .message-preview.unread {
            font-weight: 500;
            color: #1a1a1a;
        }
        .search-bar {
            position: relative;
            margin: 12px 16px;
        }
        .search-bar input {
            width: 100%;
            padding: 12px 16px 12px 44px;
            border-radius: 24px;
            border: 1px solid #e5e7eb;
            font-size: 14px;
            outline: none;
            background-color: #f3f4f6;
            transition: all 0.2s;
        }
        .search-bar input:focus {
            background-color: #fff;
            border-color: #2563eb;
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }
        .search-bar i {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
        }
        .tab-container {
            position: sticky;
            top: 56px;
            background: #fff;
            z-index: 5;
            border-bottom: 1px solid #f0f0f0;
        }
        .tabs {
            display: flex;
            overflow-x: auto;
            padding: 0 8px;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
        }
        .tabs::-webkit-scrollbar {
            display: none;
        }
        .floating-button {
            position: fixed;
            right: 20px;
            bottom: 80px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: #2563eb;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
            z-index: 90;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .floating-button:active {
            transform: scale(0.95);
            box-shadow: 0 2px 8px rgba(37, 99, 235, 0.4);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex space-x-1">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="flex items-center">
            <h1 class="text-lg font-bold">消息</h1>
        </div>
        <div class="flex space-x-4">
            <i class="fas fa-search text-gray-600"></i>
            <i class="fas fa-ellipsis-v text-gray-600"></i>
        </div>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
        <i class="fas fa-search"></i>
        <input type="text" placeholder="搜索消息、联系人...">
    </div>

    <!-- 标签页 -->
    <div class="tab-container">
        <div class="tabs">
            <a href="#" class="tab-item active">全部</a>
            <a href="#" class="tab-item">未读</a>
            <a href="#" class="tab-item">家人</a>
            <a href="#" class="tab-item">护理员</a>
            <a href="#" class="tab-item">系统</a>
            <a href="#" class="tab-item">服务</a>
        </div>
    </div>

    <!-- 消息列表 -->
    <main class="pb-20">
        <!-- 今日 -->
        <div class="px-4 py-2 text-xs text-gray-500 bg-gray-50">今天</div>
        
        <!-- 消息项 1 -->
        <a href="#" class="message-item unread">
            <div class="avatar">
                <i class="fas fa-user-nurse"></i>
                <span class="badge">3</span>
            </div>
            <div class="message-content">
                <div class="message-header">
                    <div class="message-sender">张护士</div>
                    <div class="message-time">10:30</div>
                </div>
                <div class="message-preview unread">您今天的血压测量结果正常，继续保持良好的生活习惯哦~</div>
            </div>
        </a>
        
        <!-- 消息项 2 -->
        <a href="#" class="message-item unread">
            <div class="avatar">
                <i class="fas fa-utensils"></i>
                <span class="badge">1</span>
            </div>
            <div class="message-content">
                <div class="message-header">
                    <div class="message-sender">营养餐配送</div>
                    <div class="message-time">09:15</div>
                </div>
                <div class="message-preview unread">您今日的营养午餐已送达，请及时取用</div>
            </div>
        </a>
        
        <!-- 消息项 3 -->
        <a href="#" class="message-item">
            <div class="avatar">
                <i class="fas fa-user-friends"></i>
            </div>
            <div class="message-content">
                <div class="message-header">
                    <div class="message-sender">家庭群聊</div>
                    <div class="message-time">08:45</div>
                </div>
                <div class="message-preview">儿子：爸爸，这周末我们来看您</div>
            </div>
        </a>
        
        <!-- 昨日 -->
        <div class="px-4 py-2 text-xs text-gray-500 bg-gray-50">昨天</div>
        
        <!-- 消息项 4 -->
        <a href="#" class="message-item">
            <div class="avatar" style="background-color: #f0fdf4;">
                <i class="fas fa-calendar-check" style="color: #16a34a;"></i>
            </div>
            <div class="message-content">
                <div class="message-header">
                    <div class="message-sender">服务提醒</div>
                    <div class="message-time">昨天</div>
                </div>
                <div class="message-preview">您的理发服务预约已确认，明天下午3点</div>
            </div>
        </a>
        
        <!-- 消息项 5 -->
        <a href="#" class="message-item">
            <div class="avatar">
                <i class="fas fa-heartbeat"></i>
            </div>
            <div class="message-content">
                <div class="message-header">
                    <div class="message-sender">健康日报</div>
                    <div class="message-time">昨天</div>
                </div>
                <div class="message-preview">您昨天的步数达到目标，继续保持！</div>
            </div>
        </a>
        
        <!-- 更早 -->
        <div class="px-4 py-2 text-xs text-gray-500 bg-gray-50">更早</div>
        
        <!-- 消息项 6 -->
        <a href="#" class="message-item">
            <div class="avatar">
                <i class="fas fa-bell"></i>
            </div>
            <div class="message-content">
                <div class="message-header">
                    <div class="message-sender">系统通知</div>
                    <div class="message-time">周一</div>
                </div>
                <div class="message-preview">系统维护通知：本周五晚10点至12点进行系统升级</div>
            </div>
        </a>
        
        <!-- 消息项 7 -->
        <a href="#" class="message-item">
            <div class="avatar">
                <i class="fas fa-comment-medical"></i>
            </div>
            <div class="message-content">
                <div class="message-header">
                    <div class="message-sender">健康小贴士</div>
                    <div class="message-time">周日</div>
                </div>
                <div class="message-preview">夏季防暑降温小知识：多喝水，避免高温时段外出</div>
            </div>
        </a>
    </main>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <a href="home.html" class="nav-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="health.html" class="nav-item">
            <i class="fas fa-heartbeat"></i>
            <span>健康</span>
        </a>
        <a href="services.html" class="nav-item">
            <i class="fas fa-concierge-bell"></i>
            <span>服务</span>
        </a>
        <a href="messages.html" class="nav-item active">
            <i class="fas fa-comment-alt"></i>
            <span>消息</span>
            <span class="absolute top-0 right-0 inline-block w-2 h-2 bg-red-500 rounded-full"></span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>

    <!-- 新建消息按钮 -->
    <a href="#" class="floating-button">
        <i class="fas fa-edit text-xl"></i>
    </a>

    <script>
        // 导航激活状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 标签切换
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.tab-item').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 消息项点击效果
        document.querySelectorAll('.message-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                // 移除未读状态
                this.classList.remove('unread');
                const badge = this.querySelector('.badge');
                if (badge) badge.remove();
                
                // 在实际应用中，这里会跳转到聊天详情页
                // window.location.href = 'chat-detail.html';
            });
        });
    </script>
</body>
</html>
