<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康数据 - 智慧养老平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f8f9fa;
        }
        .status-bar {
            height: 44px;
            background: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            color: #000;
        }
        .nav-bar {
            height: 56px;
            background: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 56px;
            background: #ffffff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
            padding: 0 16px;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #666;
            font-size: 12px;
        }
        .nav-item.active {
            color: #2563eb;
        }
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .card {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            overflow: hidden;
            margin-bottom: 16px;
        }
        .tab-item {
            padding: 12px 0;
            text-align: center;
            font-size: 14px;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }
        .tab-item.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
            font-weight: 500;
        }
        .metric-card {
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #f0f7ff 0%, #e6f0ff 100%);
        }
        .metric-value {
            font-size: 28px;
            font-weight: 700;
            color: #2563eb;
            margin: 8px 0 4px;
        }
        .metric-label {
            font-size: 14px;
            color: #666;
            display: flex;
            align-items: center;
        }
        .trend-up {
            color: #10b981;
            margin-left: 4px;
        }
        .trend-down {
            color: #ef4444;
            margin-left: 4px;
        }
        .health-tip {
            background: #f8f9fa;
            border-left: 4px solid #2563eb;
            padding: 12px;
            margin: 16px 0;
            border-radius: 0 8px 8px 0;
        }
        .medication-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .medication-item:last-child {
            border-bottom: none;
        }
        .medication-time {
            font-weight: 500;
            color: #333;
            min-width: 80px;
        }
        .medication-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin: 0 12px;
        }
        .medication-dot.taken {
            background-color: #10b981;
        }
        .medication-dot.pending {
            background-color: #f59e0b;
        }
        .medication-dot.missed {
            background-color: #ef4444;
        }
        .medication-info {
            flex: 1;
        }
        .medication-name {
            font-weight: 500;
            margin-bottom: 4px;
        }
        .medication-desc {
            font-size: 12px;
            color: #666;
        }
        .medication-status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 10px;
            margin-left: 8px;
        }
        .status-taken {
            background-color: #d1fae5;
            color: #065f46;
        }
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        .status-missed {
            background-color: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex space-x-1">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="flex items-center">
            <a href="home.html" class="mr-4">
                <i class="fas fa-arrow-left text-gray-600"></i>
            </a>
            <h1 class="text-lg font-bold">健康数据</h1>
        </div>
        <div class="flex space-x-4">
            <i class="fas fa-calendar-alt text-gray-600"></i>
            <i class="fas fa-ellipsis-v text-gray-600"></i>
        </div>
    </div>

    <!-- 主内容 -->
    <main class="p-4 pb-20">
        <!-- 日期选择 -->
        <div class="flex items-center justify-between mb-4 overflow-x-auto pb-2">
            <div class="flex space-x-2">
                <button class="px-4 py-2 rounded-full bg-white text-sm font-medium shadow-sm">
                    今天
                </button>
                <button class="px-4 py-2 rounded-full bg-white text-sm font-medium shadow-sm">
                    周
                </button>
                <button class="px-4 py-2 rounded-full bg-blue-500 text-white text-sm font-medium shadow-sm">
                    月
                </button>
                <button class="px-4 py-2 rounded-full bg-white text-sm font-medium shadow-sm">
                    年
                </button>
            </div>
            <div class="text-sm text-gray-500">
                2025年7月
            </div>
        </div>

        <!-- 健康概览 -->
        <div class="card p-4 mb-4">
            <h3 class="font-bold text-gray-800 mb-3">健康概览</h3>
            <div class="grid grid-cols-2 gap-3">
                <div class="metric-card">
                    <div class="flex items-center">
                        <i class="fas fa-heartbeat text-red-400 mr-2"></i>
                        <span class="text-sm">心率</span>
                    </div>
                    <div class="metric-value">78</div>
                    <div class="metric-label">
                        次/分钟
                        <span class="trend-down"><i class="fas fa-arrow-down"></i> 2%</span>
                    </div>
                </div>
                <div class="metric-card">
                    <div class="flex items-center">
                        <i class="fas fa-tint text-blue-400 mr-2"></i>
                        <span class="text-sm">血压</span>
                    </div>
                    <div class="metric-value">125/80</div>
                    <div class="metric-label">
                        mmHg
                        <span class="trend-up"><i class="fas fa-arrow-up"></i> 正常</span>
                    </div>
                </div>
                <div class="metric-card">
                    <div class="flex items-center">
                        <i class="fas fa-walking text-green-400 mr-2"></i>
                        <span class="text-sm">步数</span>
                    </div>
                    <div class="metric-value">3,245</div>
                    <div class="metric-label">
                        步
                        <span class="trend-up"><i class="fas fa-arrow-up"></i> 15%</span>
                    </div>
                </div>
                <div class="metric-card">
                    <div class="flex items-center">
                        <i class="fas fa-moon text-purple-400 mr-2"></i>
                        <span class="text-sm">睡眠</span>
                    </div>
                    <div class="metric-value">7.5h</div>
                    <div class="metric-label">
                        昨晚
                        <span class="trend-up"><i class="fas fa-arrow-up"></i> 0.5h</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 健康图表 -->
        <div class="card p-4 mb-4">
            <div class="flex justify-between items-center mb-3">
                <h3 class="font-bold text-gray-800">心率趋势</h3>
                <select class="text-sm border rounded px-2 py-1">
                    <option>本周</option>
                    <option>本月</option>
                    <option>本季</option>
                </select>
            </div>
            <div style="height: 200px;">
                <canvas id="heartRateChart"></canvas>
            </div>
        </div>

        <!-- 用药提醒 -->
        <div class="card p-4">
            <div class="flex justify-between items-center mb-3">
                <h3 class="font-bold text-gray-800">今日用药</h3>
                <a href="#" class="text-blue-500 text-sm">全部 <i class="fas fa-chevron-right text-xs"></i></a>
            </div>
            
            <div class="medication-item">
                <div class="medication-time">08:00</div>
                <div class="medication-dot taken"></div>
                <div class="medication-info">
                    <div class="flex items-center">
                        <span class="medication-name">阿司匹林肠溶片</span>
                        <span class="medication-status status-taken">已服用</span>
                    </div>
                    <div class="medication-desc">100mg × 1片 · 早餐后</div>
                </div>
                <i class="fas fa-check-circle text-green-500"></i>
            </div>
            
            <div class="medication-item">
                <div class="medication-time">12:30</div>
                <div class="medication-dot pending"></div>
                <div class="medication-info">
                    <div class="flex items-center">
                        <span class="medication-name">硝苯地平缓释片</span>
                        <span class="medication-status status-pending">待服用</span>
                    </div>
                    <div class="medication-desc">20mg × 1片 · 午餐前</div>
                </div>
                <button class="text-blue-500 text-sm font-medium">确认</button>
            </div>
            
            <div class="medication-item">
                <div class="medication-time">19:00</div>
                <div class="medication-dot"></div>
                <div class="medication-info">
                    <div class="flex items-center">
                        <span class="medication-name">阿托伐他汀钙片</span>
                        <span class="medication-status">未到时间</span>
                    </div>
                    <div class="medication-desc">10mg × 1片 · 晚餐后</div>
                </div>
            </div>
        </div>

        <!-- 健康小贴士 -->
        <div class="health-tip mt-4">
            <div class="text-sm font-medium text-gray-800 mb-1">健康小贴士</div>
            <p class="text-sm text-gray-600">
                根据您的健康数据，建议每天保持30分钟以上的有氧运动，有助于维持心血管健康。
            </p>
        </div>
    </main>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <a href="home.html" class="nav-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="health.html" class="nav-item active">
            <i class="fas fa-heartbeat"></i>
            <span>健康</span>
        </a>
        <a href="services.html" class="nav-item">
            <i class="fas fa-concierge-bell"></i>
            <span>服务</span>
        </a>
        <a href="messages.html" class="nav-item">
            <i class="fas fa-comment-alt"></i>
            <span>消息</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>

    <script>
        // 心率图表
        const heartRateCtx = document.getElementById('heartRateChart').getContext('2d');
        const heartRateChart = new Chart(heartRateCtx, {
            type: 'line',
            data: {
                labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                datasets: [{
                    label: '心率',
                    data: [76, 78, 75, 80, 82, 79, 78],
                    borderColor: '#ef4444',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    tension: 0.3,
                    fill: true,
                    pointBackgroundColor: '#fff',
                    pointBorderColor: '#ef4444',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 60,
                        max: 100,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            stepSize: 10
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // 导航激活状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
