<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务预约 - 智慧养老平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f8f9fa;
        }
        .status-bar {
            height: 44px;
            background: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            color: #000;
        }
        .nav-bar {
            height: 56px;
            background: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 56px;
            background: #ffffff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #f0f0f0;
            padding: 0 16px;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #666;
            font-size: 12px;
        }
        .nav-item.active {
            color: #2563eb;
        }
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }
        .card {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            overflow: hidden;
            margin-bottom: 16px;
        }
        .service-category {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 8px;
            border-radius: 12px;
            background: #f8f9fa;
            transition: all 0.2s;
            text-align: center;
        }
        .service-category:active {
            background: #e9ecef;
            transform: scale(0.98);
        }
        .service-category i {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 20px;
            color: white;
        }
        .service-item {
            display: flex;
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            align-items: center;
        }
        .service-item:last-child {
            border-bottom: none;
        }
        .service-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: white;
        }
        .service-info {
            flex: 1;
        }
        .service-name {
            font-weight: 500;
            margin-bottom: 2px;
        }
        .service-desc {
            font-size: 12px;
            color: #666;
        }
        .service-price {
            font-weight: 600;
            color: #2563eb;
            margin-left: 8px;
            white-space: nowrap;
        }
        .badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
            margin-left: 6px;
            vertical-align: middle;
        }
        .badge-hot {
            background-color: #fee2e2;
            color: #dc2626;
        }
        .badge-new {
            background-color: #dbeafe;
            color: #1d4ed8;
        }
        .search-bar {
            position: relative;
            margin: 12px 16px;
        }
        .search-bar input {
            width: 100%;
            padding: 12px 16px 12px 44px;
            border-radius: 24px;
            border: 1px solid #e5e7eb;
            font-size: 14px;
            outline: none;
            transition: all 0.2s;
        }
        .search-bar input:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }
        .search-bar i {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
        }
        .tab-item {
            padding: 12px 0;
            text-align: center;
            font-size: 14px;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
            position: relative;
        }
        .tab-item.active {
            color: #2563eb;
            font-weight: 500;
        }
        .tab-item.active:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 3px;
            background-color: #2563eb;
            border-radius: 2px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex space-x-1">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <div class="flex items-center">
            <a href="home.html" class="mr-4">
                <i class="fas fa-arrow-left text-gray-600"></i>
            </a>
            <h1 class="text-lg font-bold">服务预约</h1>
        </div>
        <div class="flex space-x-4">
            <i class="fas fa-calendar-alt text-gray-600"></i>
            <i class="fas fa-filter text-gray-600"></i>
        </div>
    </div>

    <!-- 主内容 -->
    <main class="pb-20">
        <!-- 搜索栏 -->
        <div class="search-bar">
            <i class="fas fa-search"></i>
            <input type="text" placeholder="搜索服务项目...">
        </div>

        <!-- 服务分类 -->
        <div class="px-4 mb-4">
            <h3 class="font-bold text-gray-800 mb-3">服务分类</h3>
            <div class="grid grid-cols-4 gap-3">
                <a href="#" class="service-category">
                    <i class="fas fa-utensils bg-blue-500"></i>
                    <span class="text-xs">餐饮服务</span>
                </a>
                <a href="#" class="service-category">
                    <i class="fas fa-bath bg-green-500"></i>
                    <span class="text-xs">助浴服务</span>
                </a>
                <a href="#" class="service-category">
                    <i class="fas fa-user-nurse bg-purple-500"></i>
                    <span class="text-xs">护理服务</span>
                </a>
                <a href="#" class="service-category">
                    <i class="fas fa-ellipsis-h bg-gray-400"></i>
                    <span class="text-xs">更多</span>
                </a>
            </div>
        </div>

        <!-- 服务列表 -->
        <div class="card mx-4 mb-4">
            <div class="flex border-b border-gray-100">
                <div class="flex-1 text-center">
                    <a href="#" class="tab-item active">全部服务</a>
                </div>
                <div class="flex-1 text-center">
                    <a href="#" class="tab-item">收藏</a>
                </div>
                <div class="flex-1 text-center">
                    <a href="#" class="tab-item">历史</a>
                </div>
            </div>
            
            <div class="divide-y divide-gray-100">
                <!-- 服务项 1 -->
                <a href="#" class="service-item">
                    <div class="service-icon" style="background-color: #3b82f6;">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <div class="service-info">
                        <div class="flex items-center">
                            <span class="service-name">营养午餐套餐</span>
                            <span class="badge badge-hot">热销</span>
                        </div>
                        <div class="service-desc">三菜一汤，适合老年人营养需求</div>
                    </div>
                    <div class="service-price">¥25</div>
                    <i class="fas fa-chevron-right text-gray-400 ml-2"></i>
                </a>
                
                <!-- 服务项 2 -->
                <a href="#" class="service-item">
                    <div class="service-icon" style="background-color: #10b981;">
                        <i class="fas fa-bath"></i>
                    </div>
                    <div class="service-info">
                        <div class="flex items-center">
                            <span class="service-name">助浴服务</span>
                            <span class="badge badge-new">新服务</span>
                        </div>
                        <div class="service-desc">专业护理人员协助洗浴，安全舒适</div>
                    </div>
                    <div class="service-price">¥80/次</div>
                    <i class="fas fa-chevron-right text-gray-400 ml-2"></i>
                </a>
                
                <!-- 服务项 3 -->
                <a href="#" class="service-item">
                    <div class="service-icon" style="background-color: #8b5cf6;">
                        <i class="fas fa-user-nurse"></i>
                    </div>
                    <div class="service-info">
                        <div class="service-name">日常护理</div>
                        <div class="service-desc">专业护理人员上门服务，包括测量血压、血糖等</div>
                    </div>
                    <div class="service-price">¥60/次</div>
                    <i class="fas fa-chevron-right text-gray-400 ml-2"></i>
                </a>
                
                <!-- 服务项 4 -->
                <a href="#" class="service-item">
                    <div class="service-icon" style="background-color: #f59e0b;">
                        <i class="fas fa-cut"></i>
                    </div>
                    <div class="service-info">
                        <div class="service-name">理发服务</div>
                        <div class="service-desc">专业理发师上门服务，提供多种发型选择</div>
                    </div>
                    <div class="service-price">¥35/次</div>
                    <i class="fas fa-chevron-right text-gray-400 ml-2"></i>
                </a>
                
                <!-- 服务项 5 -->
                <a href="#" class="service-item">
                    <div class="service-icon" style="background-color: #ec4899;">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <div class="service-info">
                        <div class="service-name">陪伴服务</div>
                        <div class="service-desc">专业陪护人员陪伴聊天、散步、阅读等</div>
                    </div>
                    <div class="service-price">¥40/小时</div>
                    <i class="fas fa-chevron-right text-gray-400 ml-2"></i>
                </a>
            </div>
        </div>
        
        <!-- 推荐服务 -->
        <div class="px-4 mb-4">
            <div class="flex items-center justify-between mb-3">
                <h3 class="font-bold text-gray-800">为您推荐</h3>
                <a href="#" class="text-blue-500 text-sm">更多 <i class="fas fa-chevron-right text-xs"></i></a>
            </div>
            <div class="grid grid-cols-2 gap-3">
                <div class="bg-white rounded-xl overflow-hidden shadow-sm">
                    <div class="h-24 bg-blue-100 flex items-center justify-center">
                        <i class="fas fa-heartbeat text-3xl text-blue-500"></i>
                    </div>
                    <div class="p-3">
                        <h4 class="font-medium text-sm mb-1">中医理疗</h4>
                        <p class="text-xs text-gray-500 mb-2">缓解疼痛，调理身体</p>
                        <div class="flex justify-between items-center">
                            <span class="text-blue-500 font-medium text-sm">¥120</span>
                            <button class="text-blue-500 text-xs font-medium">预约</button>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-xl overflow-hidden shadow-sm">
                    <div class="h-24 bg-green-100 flex items-center justify-center">
                        <i class="fas fa-dumbbell text-3xl text-green-500"></i>
                    </div>
                    <div class="p-3">
                        <h4 class="font-medium text-sm mb-1">康复训练</h4>
                        <p class="text-xs text-gray-500 mb-2">专业指导，科学锻炼</p>
                        <div class="flex justify-between items-center">
                            <span class="text-blue-500 font-medium text-sm">¥150</span>
                            <button class="text-blue-500 text-xs font-medium">预约</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <a href="home.html" class="nav-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="health.html" class="nav-item">
            <i class="fas fa-heartbeat"></i>
            <span>健康</span>
        </a>
        <a href="services.html" class="nav-item active">
            <i class="fas fa-concierge-bell"></i>
            <span>服务</span>
        </a>
        <a href="messages.html" class="nav-item">
            <i class="fas fa-comment-alt"></i>
            <span>消息</span>
        </a>
        <a href="profile.html" class="nav-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </div>

    <script>
        // 导航激活状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 标签切换
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.tab-item').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
