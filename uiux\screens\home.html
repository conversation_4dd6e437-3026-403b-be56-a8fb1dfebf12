<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 智慧养老平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap');
        
        :root {
            --primary: #4f46e5;
            --primary-light: #818cf8;
            --secondary: #10b981;
            --accent: #f59e0b;
            --danger: #ef4444;
            --success: #10b981;
            --warning: #f59e0b;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-400: #9ca3af;
            --gray-600: #4b5563;
            --gray-800: #1f2937;
        }
        
        * {
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: var(--gray-50);
            color: var(--gray-800);
            margin: 0;
            padding: 0;
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .status-bar {
            height: 44px;
            background: #ffffff;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            color: #000;
            position: sticky;
            top: 0;
            z-index: 50;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.8);
        }
        
        .nav-bar {
            height: 64px;
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            position: relative;
            box-shadow: 0 2px 10px rgba(79, 70, 229, 0.1);
        }
        
        .nav-title {
            font-size: 20px;
            font-weight: 600;
            letter-spacing: 0.5px;
            margin: 0;
        }
        
        .nav-actions {
            display: flex;
            gap: 16px;
        }
        
        .nav-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.15);
            color: white;
            transition: all 0.2s ease;
        }
        
        .nav-icon:active {
            transform: scale(0.95);
            background: rgba(255, 255, 255, 0.25);
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: #ffffff;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid var(--gray-100);
            padding: 8px 0;
            z-index: 40;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: var(--gray-400);
            font-size: 10px;
            font-weight: 500;
            text-decoration: none;
            width: 25%;
            height: 100%;
            transition: all 0.2s ease;
            position: relative;
            padding: 4px 0;
        }
        
        .nav-item i {
            font-size: 20px;
            margin-bottom: 4px;
            transition: all 0.2s ease;
        }
        
        .nav-item.active {
            color: var(--primary);
        }
        
        .nav-item.active i {
            transform: translateY(-2px);
        }
        
        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 3px;
            background: var(--primary);
            border-radius: 3px 3px 0 0;
        }
        
        .card {
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.03);
            overflow: hidden;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border: 1px solid rgba(0, 0, 0, 0.03);
        }
        
        .card:active {
            transform: translateY(2px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .health-metric {
            padding: 16px;
            border-radius: 14px;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .health-metric::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0));
            z-index: 1;
        }
        
        .health-metric .content {
            position: relative;
            z-index: 2;
            width: 100%;
        }
        
        .health-metric .value {
            font-size: 24px;
            font-weight: 700;
            margin: 4px 0;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            font-feature-settings: "tnum";
        }
        
        .health-metric .label {
            font-size: 12px;
            opacity: 0.9;
            font-weight: 500;
            letter-spacing: 0.3px;
            text-shadow: 0 1px 1px rgba(0,0,0,0.1);
        }
        
        .health-metric .trend {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-top: 4px;
        }
        
        .trend.up { color: #10b981; }
        .trend.down { color: #ef4444; }
        
        .service-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            padding: 0 4px;
        }
        
        .service-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 8px;
            border-radius: 12px;
            background: white;
            transition: all 0.2s ease;
            text-decoration: none;
            border: 1px solid var(--gray-100);
        }
        
        .service-item:active {
            transform: scale(0.96);
            background: var(--gray-50);
        }
        
        .service-item i {
            width: 44px;
            height: 44px;
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 20px;
            color: white;
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
            transition: all 0.2s ease;
        }
        
        .service-item .label {
            font-size: 12px;
            font-weight: 500;
            color: var(--gray-700);
            text-align: center;
            line-height: 1.3;
        }
        
        .activity-item {
            display: flex;
            padding: 14px 0;
            border-bottom: 1px solid var(--gray-100);
            transition: background 0.2s ease;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-item:active {
            background: var(--gray-50);
        }
        
        .activity-time {
            font-size: 12px;
            color: var(--gray-400);
            margin-right: 12px;
            min-width: 60px;
            font-feature-settings: "tnum";
            font-weight: 500;
        }
        
        .activity-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: var(--primary);
            margin-top: 6px;
            margin-right: 12px;
            flex-shrink: 0;
            position: relative;
            z-index: 1;
        }
        
        .activity-dot::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 16px;
            height: 16px;
            background: rgba(79, 70, 229, 0.1);
            border-radius: 50%;
            z-index: -1;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-weight: 500;
            margin-bottom: 4px;
            color: var(--gray-800);
            font-size: 14px;
        }
        
        .activity-desc {
            font-size: 12px;
            color: var(--gray-500);
            line-height: 1.4;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding: 0 4px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray-800);
            margin: 0;
            position: relative;
            padding-left: 12px;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 14px;
            background: var(--primary);
            border-radius: 2px;
        }
        
        .section-link {
            font-size: 12px;
            color: var(--primary);
            text-decoration: none;
            display: flex;
            align-items: center;
            font-weight: 500;
        }
        
        .section-link i {
            font-size: 10px;
            margin-left: 4px;
            transition: transform 0.2s ease;
        }
        
        .section-link:active i {
            transform: translateX(2px);
        }
        
        .welcome-card {
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            border-radius: 16px;
            padding: 20px;
            color: white;
            position: relative;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 10px 25px -5px rgba(79, 70, 229, 0.2);
        }
        
        .welcome-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
            z-index: 1;
        }
        
        .welcome-content {
            position: relative;
            z-index: 2;
        }
        
        .welcome-text {
            margin-bottom: 20px;
        }
        
        .welcome-greeting {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
            opacity: 0.9;
        }
        
        .welcome-date {
            font-size: 13px;
            opacity: 0.8;
            font-weight: 400;
        }
        
        .weather-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 12px 16px;
        }
        
        .weather-temp {
            font-size: 24px;
            font-weight: 700;
            margin-right: 8px;
            display: flex;
            align-items: center;
        }
        
        .weather-icon {
            font-size: 28px;
            margin-right: 12px;
            color: #ffeb3b;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }
        
        .weather-details {
            text-align: right;
        }
        
        .weather-desc {
            font-size: 13px;
            margin-bottom: 4px;
            font-weight: 500;
        }
        
        .weather-extra {
            font-size: 11px;
            opacity: 0.9;
            display: flex;
            gap: 8px;
        }
        
        .weather-extra span {
            display: flex;
            align-items: center;
        }
        
        .weather-extra i {
            font-size: 10px;
            margin-right: 4px;
        }
        
        .floating-action-button {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: var(--primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(79, 70, 229, 0.3);
            z-index: 30;
            transition: all 0.2s ease;
            border: none;
            outline: none;
        }
        
        .floating-action-button:active {
            transform: scale(0.95);
            box-shadow: 0 2px 10px rgba(79, 70, 229, 0.4);
        }
        
        .floating-action-button i {
            font-size: 24px;
        }
        
        .badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            line-height: 1.4;
        }
        
        .badge-success {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }
        
        .badge-warning {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }
        
        .badge-danger {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex items-center">
            <i class="fas fa-signal mr-1"></i>
            <i class="fas fa-wifi mr-1"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar">
        <h1 class="nav-title">智享颐年</h1>
        <div class="nav-actions">
            <button class="nav-icon">
                <i class="fas fa-search"></i>
            </button>
            <button class="nav-icon">
                <i class="fas fa-bell"></i>
                <span class="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
            </button>
        </div>
    </div>

    <!-- 主内容 -->
    <main class="px-4 pb-24 pt-2">
        <!-- 欢迎区域 -->
        <div class="welcome-card">
            <div class="welcome-content">
                <div class="welcome-text">
                    <div class="welcome-greeting">早上好，张爷爷</div>
                    <div class="welcome-date">2025年7月3日 星期四</div>
                </div>
                
                <!-- 天气信息 -->
                <div class="weather-info">
                    <div class="flex items-center">
                        <i class="fas fa-sun weather-icon"></i>
                        <div class="weather-temp">28<small>°C</small></div>
                    </div>
                    <div class="weather-details">
                        <div class="weather-desc">晴朗 • 空气质量 <span class="badge badge-success">优</span></div>
                        <div class="weather-extra">
                            <span><i class="fas fa-wind"></i> 2级</span>
                            <span><i class="fas fa-tint"></i> 45%</span>
                            <span><i class="fas fa-calendar-day"></i> 3项</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 健康数据 -->
        <div class="mb-6">
            <div class="section-header">
                <h2 class="section-title">健康数据</h2>
                <a href="health.html" class="section-link">详情 <i class="fas fa-chevron-right"></i></a>
            </div>
            
            <div class="grid grid-cols-2 gap-3">
                <div class="health-metric" style="background: linear-gradient(135deg, #6366f1, #8b5cf6);">
                    <div class="content">
                        <div class="label">心率</div>
                        <div class="value">72</div>
                        <div class="trend up">
                            <i class="fas fa-arrow-up mr-1"></i> 2%
                        </div>
                    </div>
                </div>
                <div class="health-metric" style="background: linear-gradient(135deg, #10b981, #34d399);">
                    <div class="content">
                        <div class="label">血氧</div>
                        <div class="value">98<small>%</small></div>
                        <div class="trend stable">
                            <i class="fas fa-equals mr-1"></i> 稳定
                        </div>
                    </div>
                </div>
                <div class="health-metric" style="background: linear-gradient(135deg, #f59e0b, #fbbf24);">
                    <div class="content">
                        <div class="label">血压</div>
                        <div class="value">125/80</div>
                        <div class="trend down">
                            <i class="fas fa-arrow-down mr-1"></i> 正常
                        </div>
                    </div>
                </div>
                <div class="health-metric" style="background: linear-gradient(135deg, #ef4444, #f87171);">
                    <div class="content">
                        <div class="label">体温</div>
                        <div class="value">36.5<small>°C</small></div>
                        <div class="trend stable">
                            <i class="fas fa-check-circle mr-1"></i> 正常
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷服务 -->
        <div class="mb-6">
            <div class="section-header">
                <h2 class="section-title">快捷服务</h2>
                <a href="services.html" class="section-link">全部 <i class="fas fa-chevron-right"></i></a>
            </div>
            
            <div class="service-grid">
                <a href="services.html" class="service-item">
                    <i class="fas fa-utensils"></i>
                    <span class="label">餐饮服务</span>
                </a>
                <a href="services.html" class="service-item">
                    <i class="fas fa-bath"></i>
                    <span class="label">助浴服务</span>
                </a>
                <a href="services.html" class="service-item">
                    <i class="fas fa-hands"></i>
                    <span class="label">护理服务</span>
                </a>
                <a href="services.html" class="service-item">
                    <i class="fas fa-procedures"></i>
                    <span class="label">医疗服务</span>
                </a>
                <a href="services.html" class="service-item">
                    <i class="fas fa-tshirt"></i>
                    <span class="label">洗衣服务</span>
                </a>
                <a href="services.html" class="service-item">
                    <i class="fas fa-broom"></i>
                    <span class="label">清洁服务</span>
                </a>
                <a href="services.html" class="service-item">
                    <i class="fas fa-car"></i>
                    <span class="label">出行服务</span>
                </a>
                <a href="services.html" class="service-item">
                    <i class="fas fa-ellipsis-h"></i>
                    <span class="label">更多服务</span>
                </a>
            </div>
        </div>

        <!-- 今日活动 -->
        <div class="card p-4 mb-6">
            <div class="section-header" style="margin-bottom: 16px;">
                <h2 class="section-title">今日活动</h2>
                <a href="#" class="section-link">查看全部 <i class="fas fa-chevron-right"></i></a>
            </div>
            
            <div class="space-y-1">
                <div class="activity-item">
                    <div class="activity-time">09:30</div>
                    <div class="activity-dot"></div>
                    <div class="activity-content">
                        <div class="activity-title">晨间运动 <span class="badge badge-success">进行中</span></div>
                        <div class="activity-desc">在护理人员陪同下进行30分钟晨间散步，请穿舒适运动鞋</div>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-time">12:00</div>
                    <div class="activity-dot"></div>
                    <div class="activity-content">
                        <div class="activity-title">午餐时间</div>
                        <div class="activity-desc">低盐低脂营养餐（预约号：A123）</div>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-time">15:00</div>
                    <div class="activity-dot"></div>
                    <div class="activity-content">
                        <div class="activity-title">健康检查 <span class="badge badge-warning">待确认</span></div>
                        <div class="activity-desc">定期健康检查，请到医疗室进行血压、血糖等常规检查</div>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-time">16:30</div>
                    <div class="activity-dot"></div>
                    <div class="activity-content">
                        <div class="activity-title">手工活动</div>
                        <div class="activity-desc">社区活动中心 - 手工制作课程</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 紧急联系人 -->
        <div class="card p-4 mb-6">
            <div class="section-header" style="margin-bottom: 16px;">
                <h2 class="section-title">紧急联系人</h2>
                <a href="profile.html" class="section-link">管理 <i class="fas fa-chevron-right"></i></a>
            </div>
            
            <div class="flex items-center justify-between px-2">
                <div class="flex items-center">
                    <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mr-3">
                        <i class="fas fa-phone-alt text-red-500"></i>
                    </div>
                    <div>
                        <div class="font-medium">紧急呼叫</div>
                        <div class="text-sm text-gray-500">24小时紧急联系</div>
                    </div>
                </div>
                <button class="w-10 h-10 rounded-full bg-red-500 text-white flex items-center justify-center">
                    <i class="fas fa-phone"></i>
                </button>
            </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <div class="bottom-nav">
        <a href="home.html" class="nav-item active">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="health.html" class="nav-item">
            <i class="fas fa-heartbeat"></i>
            <span>健康</span>
        </a>
        <a href="services.html" class="nav-item">
            <i class="fas fa-concierge-bell"></i>
            <span>服务</span>
        </a>
        <a href="messages.html" class="nav-item">
            <i class="fas fa-comment-alt"></i>
            <span>消息</span>
            <span class="absolute top-1 right-4 inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-500 rounded-full">3</span>
        </a>
    </div>
    
    <!-- 悬浮按钮 -->
    <button class="floating-action-button">
        <i class="fas fa-plus"></i>
    </button>

    <script>
        // 导航激活状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
                
                // 模拟页面跳转
                const target = this.getAttribute('href');
                if (target && target !== '#') {
                    setTimeout(() => {
                        // 在实际应用中取消下面的注释以启用页面跳转
                        // window.location.href = target;
                    }, 150);
                }
            });
        });

        // 服务项点击效果
        document.querySelectorAll('.service-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                // 在实际应用中，这里会跳转到相应的服务页面
                // window.location.href = 'services.html';
            });
        });
        
        // 悬浮按钮点击效果
        const fab = document.querySelector('.floating-action-button');
        if (fab) {
            fab.addEventListener('click', function() {
                // 在这里添加快捷操作的逻辑
                console.log('FAB 点击');
                // 例如显示快速操作菜单或触发某个常用功能
            });
        }
        
        // 模拟活动项点击效果
        document.querySelectorAll('.activity-item').forEach(item => {
            item.addEventListener('click', function() {
                // 在实际应用中，这里会跳转到活动详情页
                console.log('点击活动:', this.querySelector('.activity-title').textContent);
            });
        });
    </script>
</body>
</html>
