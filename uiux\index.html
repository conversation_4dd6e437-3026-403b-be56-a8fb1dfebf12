<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧养老平台 - 原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .device-frame {
            width: 375px;
            height: 812px;
            border: 12px solid #111;
            border-radius: 40px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            margin: 20px;
        }
        .device-frame::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 140px;
            height: 24px;
            background: #111;
            border-bottom-left-radius: 12px;
            border-bottom-right-radius: 12px;
            z-index: 10;
        }
        .device-screen {
            width: 100%;
            height: 100%;
            background: #f8f9fa;
            overflow: hidden;
        }
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        .prototype-card {
            background: white;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .prototype-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        .prototype-desc {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }
        .prototype-frame {
            border: 1px solid #eee;
            border-radius: 8px;
            overflow: hidden;
        }
    </style>
</head>
<body class="bg-gray-100">
    <header class="bg-blue-600 text-white p-6">
        <h1 class="text-3xl font-bold">智慧养老平台 - 原型展示</h1>
        <p class="mt-2 text-blue-100">基于PRD和用户故事设计的移动端高保真原型</p>
    </header>

    <main class="container mx-auto p-6">
        <div class="mb-8">
            <h2 class="text-2xl font-semibold mb-4">核心页面</h2>
            <div class="prototype-grid">
                <!-- 首页 -->
                <div class="prototype-card">
                    <h3 class="prototype-title">1. 首页</h3>
                    <p class="prototype-desc">用户登录后的主界面，展示关键信息和快捷入口</p>
                    <div class="prototype-frame">
                        <div class="device-frame mx-auto">
                            <div class="device-screen">
                                <iframe src="screens/home.html" frameborder="0" width="100%" height="100%"></iframe>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 健康数据 -->
                <div class="prototype-card">
                    <h3 class="prototype-title">2. 健康数据</h3>
                    <p class="prototype-desc">展示老人的健康数据统计和趋势</p>
                    <div class="prototype-frame">
                        <div class="device-frame mx-auto">
                            <div class="device-screen">
                                <iframe src="screens/health.html" frameborder="0" width="100%" height="100%"></iframe>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 服务预约 -->
                <div class="prototype-card">
                    <h3 class="prototype-title">3. 服务预约</h3>
                    <p class="prototype-desc">浏览和预约各类养老服务</p>
                    <div class="prototype-frame">
                        <div class="device-frame mx-auto">
                            <div class="device-screen">
                                <iframe src="screens/services.html" frameborder="0" width="100%" height="100%"></iframe>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 消息中心 -->
                <div class="prototype-card">
                    <h3 class="prototype-title">4. 消息中心</h3>
                    <p class="prototype-desc">查看和回复消息通知</p>
                    <div class="prototype-frame">
                        <div class="device-frame mx-auto">
                            <div class="device-screen">
                                <iframe src="screens/messages.html" frameborder="0" width="100%" height="100%"></iframe>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 个人中心 -->
                <div class="prototype-card">
                    <h3 class="prototype-title">5. 个人中心</h3>
                    <p class="prototype-desc">管理个人资料和设置</p>
                    <div class="prototype-frame">
                        <div class="device-frame mx-auto">
                            <div class="device-screen">
                                <iframe src="screens/profile.html" frameborder="0" width="100%" height="100%"></iframe>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-gray-800 text-white p-6 mt-12">
        <div class="container mx-auto">
            <p class="text-center text-gray-400 text-sm">© 2025 智慧养老平台 - 高保真原型设计</p>
        </div>
    </footer>
</body>
</html>
